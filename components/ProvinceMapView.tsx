import React, { forwardRef, memo, useCallback } from "react";
import { Alert, Platform, StyleSheet, Text, View } from "react-native";
import { Camera, MapView } from "@maplibre/maplibre-react-native";

interface ProvinceMapViewProps {
  loadMap: string;
  coordinates: [number, number];
  onDidFinishLoadingMap?: () => void;
  onDidFailLoadingMap?: () => void;
}

const ProvinceMapView = memo(
  forwardRef<any, ProvinceMapViewProps>(
    ({ loadMap, coordinates, onDidFinishLoadingMap, onDidFailLoadingMap }, ref) => {
      // Memoized event handlers for better performance
      const handleMapLoadSuccess = useCallback(() => {
        if (onDidFinishLoadingMap) {
          onDidFinishLoadingMap();
        }
      }, [onDidFinishLoadingMap]);

      const handleMapLoadError = useCallback(() => {
        console.error("MapView failed to load");
        if (onDidFailLoadingMap) {
          onDidFailLoadingMap();
        }
        Alert.alert(
          "Lỗi bản đồ",
          "<PERSON>h<PERSON>ng thể tải bản đồ. <PERSON><PERSON> lòng thử lại sau."
        );
      }, [onDidFailLoadingMap]);

      // Memoized empty functions for performance
      const handleMapPress = useCallback(() => {}, []);
      const handleMapLongPress = useCallback(() => {}, []);

      return (
        <MapView
          mapStyle={loadMap}
          style={{ flex: 1 }}
          zoomEnabled={false}
          scrollEnabled={false} // Tắt cuộn để tăng hiệu suất
          pitchEnabled={false} // Tắt nghiêng để tăng hiệu suất
          rotateEnabled={false} // Tắt xoay để tăng hiệu suất
          logoEnabled={false}
          compassEnabled={false} // Tắt la bàn để tăng hiệu suất
          attributionEnabled={false} // Tắt attribution để tăng hiệu suất
          surfaceView={true} // Sử dụng SurfaceView trên Android để tăng hiệu suất
          onPress={handleMapPress} // Ngăn chặn sự kiện nhấn vào bản đồ thay đổi vị trí marker
          onLongPress={handleMapLongPress} // Ngăn chặn sự kiện nhấn giữ vào bản đồ
          onDidFinishLoadingMap={handleMapLoadSuccess}
          onDidFailLoadingMap={handleMapLoadError}
        >
        <Camera
          ref={ref}
          zoomLevel={16} // Mức thu phóng của bản đồ
          centerCoordinate={coordinates}
          animationDuration={0} // Tắt animation để tăng hiệu suất
          followUserLocation={false} // Không theo dõi vị trí người dùng
        />
        {/* Marker tùy chỉnh thay thế PointAnnotation */}
        <View
          style={[
            styles.customMarkerWrapper,
            {
              left: "50%",
              top: "50%",
              marginLeft: -15, // Một nửa chiều rộng của marker
              marginTop: -30, // Chiều cao của marker để đặt đúng vị trí
            },
          ]}
        >
          <View style={styles.markerContainer}>
            <Text style={styles.markerText}>P</Text>
          </View>
        </View>
        </MapView>
      );
    }
  )
);

ProvinceMapView.displayName = "ProvinceMapView";

const styles = StyleSheet.create({
  // Marker styles
  markerContainer: {
    width: 30,
    height: 30,
    borderRadius: 18,
    backgroundColor: "#ffde59", // Màu primary
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "white",
    // Giảm shadow để tăng hiệu suất
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
      },
      android: {
        elevation: 3, // Giảm elevation trên Android
      },
    }),
  },
  markerText: {
    color: "#333", // Màu chữ đen để tương phản với nền vàng
    fontWeight: "bold",
    fontSize: 14,
    textAlign: "center",
    fontFamily: "Nunito",
  },
  // Tối ưu hiệu suất bằng cách giảm shadow và hiệu ứng
  customMarkerWrapper: {
    position: "absolute",
    zIndex: 1,
    pointerEvents: "none", // Cho phép các sự kiện chạm xuyên qua marker
  },
});

export default ProvinceMapView;
