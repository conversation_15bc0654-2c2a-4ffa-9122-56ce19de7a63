{"expo": {"name": "SayGo", "slug": "SayGo", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "saygo", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "vn.saygo", "appleTeamId": "X66FC83435"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/splash-icon.png", "backgroundColor": "#ffde59"}, "package": "vn.saygo"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "@maplibre/maplibre-react-native", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 400, "resizeMode": "contain", "backgroundColor": "#ffde59"}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "<PERSON> phép <PERSON><PERSON><PERSON> sử dụng vị trí của bạn để tìm kiếm địa điểm gần đây.", "locationAlwaysPermission": "Cho phép Say<PERSON><PERSON> sử dụng vị trí của bạn để tìm kiếm địa điểm gần đây ngay cả khi ứng dụng đang chạy nền.", "locationWhenInUsePermission": "Cho phép Say<PERSON><PERSON> sử dụng vị trí của bạn để tìm kiếm địa điểm gần đây khi bạn đang sử dụng ứng dụng."}], ["expo-image-picker", {"photosPermission": "<PERSON><PERSON><PERSON> cần quyền truy cập thư viện ảnh để bạn có thể chọn ảnh đại diện.", "cameraPermission": "<PERSON><PERSON><PERSON> cần quyền truy cập máy ảnh để bạn có thể chụp ảnh đại diện."}]], "experiments": {"typedRoutes": true}}}