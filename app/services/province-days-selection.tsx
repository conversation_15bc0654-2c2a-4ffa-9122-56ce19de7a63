import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useState } from "react";
import {
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { updateTripDays } from "@/store/slices/serviceSlice";

export default function ProvinceDaysSelection() {
  const insets = useSafeAreaInsets();
  const dispatch = useAppDispatch();

  // Get current selected days from Redux
  const { tripDays } = useAppSelector((state) => state.service);

  // State for selected days and custom input
  const [selectedDays, setSelectedDays] = useState(tripDays);
  const [customDays, setCustomDays] = useState("");
  const [showCustomDaysInput, setShowCustomDaysInput] = useState(false);

  const handleDaysSelect = (days: number) => {
    console.log("Selected days:", days);
    setSelectedDays(days);
    setShowCustomDaysInput(false);
    // Update Redux store and navigate back
    dispatch(updateTripDays(days));
    router.back();
  };

  const handleCustomDaysSelect = () => {
    setShowCustomDaysInput(true);
  };

  const handleCustomDaysConfirm = () => {
    if (customDays.trim() !== "") {
      const days = parseInt(customDays);
      if (!isNaN(days) && days > 0) {
        setSelectedDays(days);
        // Update Redux store and navigate back
        dispatch(updateTripDays(days));
        router.back();
        return;
      }
    }
    setShowCustomDaysInput(false);
    setCustomDays("");
  };

  const handleClose = () => {
    router.back();
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <Text style={styles.title}>Chọn số ngày thuê tài</Text>
        <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
          <Ionicons name="close" size={24} color="#333" />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {[1, 2, 3, 4, 5, 6, 7].map((days) => (
            <TouchableOpacity
              key={days}
              style={[
                styles.dayItem,
                days === selectedDays && styles.dayItemSelected,
              ]}
              onPress={() => handleDaysSelect(days)}
            >
              <Text
                style={[
                  styles.dayItemText,
                  days === selectedDays && styles.dayItemTextSelected,
                ]}
              >
                {days} ngày
              </Text>
              {days === selectedDays && (
                <Ionicons name="checkmark" size={20} color="#007AFF" />
              )}
            </TouchableOpacity>
          ))}

          {/* Custom input option */}
          {!showCustomDaysInput ? (
            <TouchableOpacity
              style={styles.dayItem}
              onPress={handleCustomDaysSelect}
            >
              <Text style={styles.dayItemText}>Tự nhập số ngày</Text>
              <Ionicons name="create-outline" size={20} color="#666" />
            </TouchableOpacity>
          ) : (
            <View style={styles.customDaysContainer}>
              <View style={styles.customInputWrapper}>
                <TextInput
                  style={styles.customDaysInput}
                  placeholder="Nhập số ngày"
                  value={customDays}
                  onChangeText={setCustomDays}
                  keyboardType="number-pad"
                  autoFocus
                  maxLength={3}
                />
              </View>
              <TouchableOpacity
                style={styles.customDaysButton}
                onPress={handleCustomDaysConfirm}
              >
                <Text style={styles.customDaysButtonText}>Xác nhận</Text>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  dayItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    backgroundColor: "white",
  },
  dayItemSelected: {
    backgroundColor: "#f0f8ff",
  },
  dayItemText: {
    fontSize: 16,
    color: "#333",
  },
  dayItemTextSelected: {
    color: Colors.primary,
    fontWeight: "600",
  },
  customDaysContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    backgroundColor: "white",
  },
  customInputWrapper: {
    flex: 1,
    marginRight: 12,
  },
  customDaysInput: {
    height: 44,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    backgroundColor: "white",
  },
  customDaysButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    minWidth: 100,
  },
  customDaysButtonText: {
    color: "#333",
    fontWeight: "600",
    fontSize: 16,
  },
});
